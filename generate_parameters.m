% =========================================================================
% Parameter Generation Script for COMSOL Simulation
%
% Description:
% This script generates 10,000 parameter combinations for the COMSOL
% simulation using Latin Hypercube Sampling (LHS).
% It creates one group of 500 samples for Cu_Cu1 using a logarithmic
% distribution and another group of 20 samples for the other 6
% parameters using a linear distribution.
% The final 10,000 combinations are saved to 'parameters_to_run.csv'.
%
% Author: Cline
% Date: August 30, 2025
% =========================================================================

clear; clc; close all;

fprintf('Step 1: Generating 10,000 parameter sets...\n');

% Define ranges for the parameters
% Group 1: Cu_Cu1 (logarithmic sampling)
n_cu1 = 500;
range_cu1 = [1e6, 1e8];

% Group 2: Other parameters (linear sampling)
n_others = 20;
vars_others = {
    'cupper_A_pos', [-0.5, 0.5];
    'cupper_C_pos', [-0.5, 0.5];
    'L_Cu_A',       [2, 3];
    'L_Cu_C',       [2, 3];
    'h_Cu_AA',      [0.4, 0.5];
    'h_Cu_CC',      [0.4, 0.5]
};

% Generate samples for Cu_Cu1 using log-spaced LHS
log_lhs_cu1 = lhsdesign(n_cu1, 1);
log_range_cu1 = log10(range_cu1);
log_samples_cu1 = log_range_cu1(1) + log_lhs_cu1 * (log_range_cu1(2) - log_range_cu1(1));
samples_cu1 = 10.^log_samples_cu1;

% Generate samples for the other 6 parameters using standard LHS
n_vars_others = size(vars_others, 1);
lhs_others = lhsdesign(n_others, n_vars_others);
samples_others = zeros(n_others, n_vars_others);
for i = 1:n_vars_others
    range = vars_others{i, 2};
    samples_others(:, i) = range(1) + lhs_others(:, i) * (range(2) - range(1));
end

% Combine the parameter sets to get 500 * 20 = 10000 total combinations
total_runs = n_cu1 * n_others;
param_table = table('Size', [total_runs, n_vars_others + 1], ...
    'VariableTypes', repmat({'double'}, 1, n_vars_others + 1), ...
    'VariableNames', ['Cu_Cu1', vars_others(:,1)']);

idx = 1;
for i = 1:n_cu1
    for j = 1:n_others
        param_table.Cu_Cu1(idx) = samples_cu1(i);
        for k = 1:n_vars_others
            param_table.(vars_others{k,1})(idx) = samples_others(j, k);
        end
        idx = idx + 1;
    end
end

% Save the generated parameters to a CSV file
output_filename = 'parameters_to_run.csv';
writetable(param_table, output_filename);
fprintf('Successfully generated and saved %d parameter sets to %s.\n', total_runs, output_filename);
