function out = M2025_8_30()
%
% M2025_8_30_batch.m - 批量仿真脚本
%
% 读取generated_parameters.csv文件中的参数进行批量COMSOL仿真
% 根据参数值生成对应的CSV文件名并导出结果

import com.comsol.model.*
import com.comsol.model.util.*

% 读取参数文件
fprintf('正在读取参数文件 generated_parameters.csv...\n');
if ~exist('generated_parameters.csv', 'file')
    error('未找到参数文件 generated_parameters.csv');
end

params_table = readtable('generated_parameters.csv');
total_runs = height(params_table);
fprintf('共读取到 %d 组参数，开始批量仿真...\n', total_runs);

% 创建输出目录
output_dir = 'batch_results';
if ~exist(output_dir, 'dir')
    mkdir(output_dir);
end

% 批量处理循环
for i = 1:total_runs
    fprintf('\n=== 正在处理第 %d/%d 组参数 ===\n', i, total_runs);

    try
        % 清理之前的模型
        ModelUtil.clear();

        % 创建新模型
        model = ModelUtil.create('Model');

        % 设置模型路径和标签
        model.modelPath('G:\diffusion model data');
        model.label(['batch_run_' num2str(i) '.mph']);
        model.description(['Batch simulation run ' num2str(i) ' of ' num2str(total_runs)]);

        % 从CSV文件读取参数并设置
        current_params = params_table(i, :);

        % 设置固定参数
        model.param.set('AMP', '1e9');
        model.param.set('cupper_width', '5');
        model.param.set('cupper_height', '2');
        model.param.set('cupper_B_pos', '1.5');
        model.param.set('cupper_D_pos', '1.4');
        model.param.set('Cu_SiO2', '9E+07');
        model.param.set('SiO2_SiO2', '4.00E+08');
        model.param.set('Si_SiO2', '2.8e7', '6e8');
        model.param.set('Cu_Si', '2.58e8');
        model.param.set('Si_Si', '2e8');
        model.param.set('Cu_Cu2', '60000000');
        model.param.set('h_Cu_B', '0.41');
        model.param.set('h_Cu_D', '0.51');
        model.param.set('L_Cu_B', '3');
        model.param.set('L_Cu_D', '3');

        % 设置从CSV读取的可变参数
        model.param.set('Cu_Cu1', num2str(current_params.Cu_Cu1));
        model.param.set('cupper_A_pos', num2str(current_params.cupper_A_pos));
        model.param.set('cupper_C_pos', num2str(current_params.cupper_C_pos));
        model.param.set('L_Cu_A', num2str(current_params.L_Cu_A));
        model.param.set('L_Cu_C', num2str(current_params.L_Cu_C));
        model.param.set('h_Cu_A', num2str(current_params.h_Cu_AA));  % 注意：CSV中是h_Cu_AA
        model.param.set('h_Cu_C', num2str(current_params.h_Cu_CC));  % 注意：CSV中是h_Cu_CC
        model.param.set('h_Cu_AA', num2str(current_params.h_Cu_AA));
        model.param.set('h_Cu_CC', num2str(current_params.h_Cu_CC));

        % 创建组件和几何
        model.component.create('comp1', true);

model.component('comp1').geom.create('geom1', 2);

model.component('comp1').mesh.create('mesh1');

model.component('comp1').geom('geom1').lengthUnit([native2unicode(hex2dec({'00' 'b5'}), 'unicode') 'm']);
model.component('comp1').geom('geom1').create('r1', 'Rectangle');
model.component('comp1').geom('geom1').feature('r1').label('Si layer1');
model.component('comp1').geom('geom1').feature('r1').set('size', [5 0.1]);
model.component('comp1').geom('geom1').feature('r1').set('base', 'center');
model.component('comp1').geom('geom1').feature('r1').set('pos', {'0' '0.05-(1-(h_Cu_B+0.2))'});
model.component('comp1').geom('geom1').feature('r1').set('selresult', true);
model.component('comp1').geom('geom1').feature('r1').set('color', '4');
model.component('comp1').geom('geom1').create('r2', 'Rectangle');
model.component('comp1').geom('geom1').feature('r2').label('SiO2 layer1');
model.component('comp1').geom('geom1').feature('r2').set('size', {'5' 'h_Cu_B+0.2'});
model.component('comp1').geom('geom1').feature('r2').set('base', 'center');
model.component('comp1').geom('geom1').feature('r2').set('pos', {'0' '-1+(h_Cu_B+0.2)/2'});
model.component('comp1').geom('geom1').feature('r2').set('selresult', true);
model.component('comp1').geom('geom1').feature('r2').set('color', '10');
model.component('comp1').geom('geom1').create('r6', 'Rectangle');
model.component('comp1').geom('geom1').feature('r6').label('SiO2 layer2');
model.component('comp1').geom('geom1').feature('r6').set('size', {'5' 'h_Cu_C+0.3'});
model.component('comp1').geom('geom1').feature('r6').set('base', 'center');
model.component('comp1').geom('geom1').feature('r6').set('pos', {'0' '-1-(h_Cu_C+0.3)/2'});
model.component('comp1').geom('geom1').feature('r6').set('selresult', true);
model.component('comp1').geom('geom1').feature('r6').set('color', '10');
model.component('comp1').geom('geom1').create('r3', 'Rectangle');
model.component('comp1').geom('geom1').feature('r3').label('Si layer2');
model.component('comp1').geom('geom1').feature('r3').set('size', [5 1]);
model.component('comp1').geom('geom1').feature('r3').set('base', 'center');
model.component('comp1').geom('geom1').feature('r3').set('pos', {'0' '-1-(h_Cu_C+0.3)-0.5'});
model.component('comp1').geom('geom1').feature('r3').set('selresult', true);
model.component('comp1').geom('geom1').feature('r3').set('color', '4');
model.component('comp1').geom('geom1').create('r4', 'Rectangle');
model.component('comp1').geom('geom1').feature('r4').label('cupper_A_pos');
model.component('comp1').geom('geom1').feature('r4').set('size', {'L_Cu_A' 'h_Cu_AA'});
model.component('comp1').geom('geom1').feature('r4').set('base', 'center');
model.component('comp1').geom('geom1').feature('r4').set('pos', {'cupper_A_pos' '-1+h_Cu_AA/2'});
model.component('comp1').geom('geom1').feature('r4').set('selresult', true);
model.component('comp1').geom('geom1').feature('r4').set('color', '8');
model.component('comp1').geom('geom1').create('r5', 'Rectangle');
model.component('comp1').geom('geom1').feature('r5').active(false);
model.component('comp1').geom('geom1').feature('r5').label('cupper_B_pos');
model.component('comp1').geom('geom1').feature('r5').set('size', {'L_Cu_B' 'h_Cu_B'});
model.component('comp1').geom('geom1').feature('r5').set('base', 'center');
model.component('comp1').geom('geom1').feature('r5').set('pos', {'cupper_B_pos' '-1+h_Cu_B/2'});
model.component('comp1').geom('geom1').feature('r5').set('selresult', true);
model.component('comp1').geom('geom1').feature('r5').set('color', '8');
model.component('comp1').geom('geom1').create('r7', 'Rectangle');
model.component('comp1').geom('geom1').feature('r7').label('cupper_C_pos');
model.component('comp1').geom('geom1').feature('r7').set('size', {'L_Cu_C' 'h_Cu_CC'});
model.component('comp1').geom('geom1').feature('r7').set('base', 'center');
model.component('comp1').geom('geom1').feature('r7').set('pos', {'cupper_C_pos' '-1-h_Cu_CC/2'});
model.component('comp1').geom('geom1').feature('r7').set('selresult', true);
model.component('comp1').geom('geom1').feature('r7').set('color', '8');
model.component('comp1').geom('geom1').create('r8', 'Rectangle');
model.component('comp1').geom('geom1').feature('r8').active(false);
model.component('comp1').geom('geom1').feature('r8').label('cupper_D_pos');
model.component('comp1').geom('geom1').feature('r8').set('size', {'L_Cu_D' 'h_Cu_D'});
model.component('comp1').geom('geom1').feature('r8').set('base', 'center');
model.component('comp1').geom('geom1').feature('r8').set('pos', {'cupper_D_pos' '-1-h_Cu_D/2'});
model.component('comp1').geom('geom1').feature('r8').set('selresult', true);
model.component('comp1').geom('geom1').feature('r8').set('color', '8');
model.component('comp1').geom('geom1').run;

model.component('comp1').material.create('mat1', 'Common');
model.component('comp1').material.create('mat2', 'Common');
model.component('comp1').material.create('mat3', 'Common');
model.component('comp1').material('mat1').selection.set([1 4]);
model.component('comp1').material('mat1').propertyGroup.create('DispersionModelSellmeierModified2', 'DispersionModelSellmeierModified2', ['Sellmeier ' native2unicode(hex2dec({'4f' 'ee'}), 'unicode')  native2unicode(hex2dec({'6b' '63'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode')  native2unicode(hex2dec({'7c' '7b'}), 'unicode')  native2unicode(hex2dec({'57' '8b'}), 'unicode') ' 2']);
model.component('comp1').material('mat1').propertyGroup.create('RefractiveIndex', 'RefractiveIndex', [native2unicode(hex2dec({'62' '98'}), 'unicode')  native2unicode(hex2dec({'5c' '04'}), 'unicode')  native2unicode(hex2dec({'73' '87'}), 'unicode') ]);
model.component('comp1').material('mat2').selection.set([5 6]);
model.component('comp1').material('mat2').propertyGroup.create('DispersionModelSellmeierModified2', 'DispersionModelSellmeierModified2', ['Sellmeier ' native2unicode(hex2dec({'4f' 'ee'}), 'unicode')  native2unicode(hex2dec({'6b' '63'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode')  native2unicode(hex2dec({'7c' '7b'}), 'unicode')  native2unicode(hex2dec({'57' '8b'}), 'unicode') ' 2']);
model.component('comp1').material('mat2').propertyGroup.create('RefractiveIndex', 'RefractiveIndex', [native2unicode(hex2dec({'62' '98'}), 'unicode')  native2unicode(hex2dec({'5c' '04'}), 'unicode')  native2unicode(hex2dec({'73' '87'}), 'unicode') ]);
model.component('comp1').material('mat3').selection.set([2 3]);
model.component('comp1').material('mat3').propertyGroup.create('DispersionModelSellmeierModified2', 'DispersionModelSellmeierModified2', ['Sellmeier ' native2unicode(hex2dec({'4f' 'ee'}), 'unicode')  native2unicode(hex2dec({'6b' '63'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode')  native2unicode(hex2dec({'7c' '7b'}), 'unicode')  native2unicode(hex2dec({'57' '8b'}), 'unicode') ' 2']);
model.component('comp1').material('mat3').propertyGroup.create('RefractiveIndex', 'RefractiveIndex', [native2unicode(hex2dec({'62' '98'}), 'unicode')  native2unicode(hex2dec({'5c' '04'}), 'unicode')  native2unicode(hex2dec({'73' '87'}), 'unicode') ]);

model.component('comp1').physics.create('ht', 'HeatTransfer', 'geom1');
model.component('comp1').physics('ht').create('bhs1', 'BoundaryHeatSource', 1);
model.component('comp1').physics('ht').feature('bhs1').selection.set([9]);
model.component('comp1').physics('ht').create('temp1', 'TemperatureBoundary', 1);
model.component('comp1').physics('ht').feature('temp1').selection.set([2]);
model.component('comp1').physics('ht').create('tc1', 'ThermalContact', 1);
model.component('comp1').physics('ht').feature('tc1').selection.set([4 8]);
model.component('comp1').physics('ht').create('tc2', 'ThermalContact', 1);
model.component('comp1').physics('ht').feature('tc2').selection.set([14]);
model.component('comp1').physics('ht').create('tc3', 'ThermalContact', 1);
model.component('comp1').physics('ht').feature('tc3').selection.set([6 10 11 12 13 15 16 17 18]);
model.component('comp1').physics('ht').create('tc4', 'ThermalContact', 1);
model.component('comp1').physics('ht').feature('tc4').selection.set([19]);
model.component('comp1').physics('ht').create('tc5', 'ThermalContact', 1);
model.component('comp1').physics('ht').create('pc1', 'PeriodicHeat', 1);
model.component('comp1').physics('ht').feature('pc1').selection.set([1 3 5 7 20 21 22 23]);

model.component('comp1').mesh('mesh1').autoMeshSize(1);

model.component('comp1').view('view1').axis.set('xmin', -2.6862988471984863);
model.component('comp1').view('view1').axis.set('xmax', 3.261265277862549);
model.component('comp1').view('view1').axis.set('ymin', -2.5694527626037598);
model.component('comp1').view('view1').axis.set('ymax', 0.799687385559082);

model.component('comp1').material('mat1').label('Si');
model.component('comp1').material('mat1').propertyGroup('def').set('thermalconductivity', {'149' '0' '0' '0' '149' '0' '0' '0' '149'});
model.component('comp1').material('mat1').propertyGroup('def').set('density', '2330');
model.component('comp1').material('mat1').propertyGroup('def').set('heatcapacity', '705');
model.component('comp1').material('mat1').propertyGroup('DispersionModelSellmeierModified2').set('ODsmc', {'1.28604141' '1.07044083' '1.10202242' '1.00585997e-2' '100'});
model.component('comp1').material('mat1').propertyGroup('DispersionModelSellmeierModified2').set('Trefsmc', '22[degC]');
model.component('comp1').material('mat1').propertyGroup('DispersionModelSellmeierModified2').set('Prefsmc', '0');
model.component('comp1').material('mat1').propertyGroup('DispersionModelSellmeierModified2').addInput('frequency');
model.component('comp1').material('mat1').propertyGroup('RefractiveIndex').addInput('frequency');
model.component('comp1').material('mat2').label('Cu');
model.component('comp1').material('mat2').propertyGroup('def').set('thermalconductivity', {'401' '0' '0' '0' '401' '0' '0' '0' '401'});
model.component('comp1').material('mat2').propertyGroup('def').set('density', '8960');
model.component('comp1').material('mat2').propertyGroup('def').set('heatcapacity', '385');
model.component('comp1').material('mat2').propertyGroup('DispersionModelSellmeierModified2').set('ODsmc', {'1.28604141' '1.07044083' '1.10202242' '1.00585997e-2' '100'});
model.component('comp1').material('mat2').propertyGroup('DispersionModelSellmeierModified2').set('Trefsmc', '22[degC]');
model.component('comp1').material('mat2').propertyGroup('DispersionModelSellmeierModified2').set('Prefsmc', '0');
model.component('comp1').material('mat2').propertyGroup('DispersionModelSellmeierModified2').addInput('frequency');
model.component('comp1').material('mat2').propertyGroup('RefractiveIndex').addInput('frequency');
model.component('comp1').material('mat3').label('SiO2');
model.component('comp1').material('mat3').propertyGroup('def').set('thermalconductivity', {'1.4' '0' '0' '0' '1.4' '0' '0' '0' '1.4'});
model.component('comp1').material('mat3').propertyGroup('def').set('density', '2200');
model.component('comp1').material('mat3').propertyGroup('def').set('heatcapacity', '740');
model.component('comp1').material('mat3').propertyGroup('DispersionModelSellmeierModified2').set('ODsmc', {'1.28604141' '1.07044083' '1.10202242' '1.00585997e-2' '100'});
model.component('comp1').material('mat3').propertyGroup('DispersionModelSellmeierModified2').set('Trefsmc', '22[degC]');
model.component('comp1').material('mat3').propertyGroup('DispersionModelSellmeierModified2').set('Prefsmc', '0');
model.component('comp1').material('mat3').propertyGroup('DispersionModelSellmeierModified2').addInput('frequency');
model.component('comp1').material('mat3').propertyGroup('RefractiveIndex').addInput('frequency');

model.component('comp1').physics('ht').prop('PhysicalModelProperty').set('dz', '5e-6');
model.component('comp1').physics('ht').feature('bhs1').set('Qb_input', '4e7');
model.component('comp1').physics('ht').feature('tc1').set('hgap', 'Si_SiO2');
model.component('comp1').physics('ht').feature('tc1').label([native2unicode(hex2dec({'63' 'a5'}), 'unicode')  native2unicode(hex2dec({'89' 'e6'}), 'unicode')  native2unicode(hex2dec({'70' 'ed'}), 'unicode')  native2unicode(hex2dec({'96' '3b'}), 'unicode') '(Si_SiO2)']);
model.component('comp1').physics('ht').feature('tc2').set('hgap', 'Cu_Cu1');
model.component('comp1').physics('ht').feature('tc2').label([native2unicode(hex2dec({'63' 'a5'}), 'unicode')  native2unicode(hex2dec({'89' 'e6'}), 'unicode')  native2unicode(hex2dec({'70' 'ed'}), 'unicode')  native2unicode(hex2dec({'96' '3b'}), 'unicode') '(Cu_Cu) 1']);
model.component('comp1').physics('ht').feature('tc3').set('hgap', 'Cu_SiO2');
model.component('comp1').physics('ht').feature('tc3').label([native2unicode(hex2dec({'63' 'a5'}), 'unicode')  native2unicode(hex2dec({'89' 'e6'}), 'unicode')  native2unicode(hex2dec({'70' 'ed'}), 'unicode')  native2unicode(hex2dec({'96' '3b'}), 'unicode') '(Cu_SiO2) ']);
model.component('comp1').physics('ht').feature('tc4').set('hgap', 'SiO2_SiO2');
model.component('comp1').physics('ht').feature('tc4').label([native2unicode(hex2dec({'63' 'a5'}), 'unicode')  native2unicode(hex2dec({'89' 'e6'}), 'unicode')  native2unicode(hex2dec({'70' 'ed'}), 'unicode')  native2unicode(hex2dec({'96' '3b'}), 'unicode') '(SiO2_SiO2) ']);
model.component('comp1').physics('ht').feature('tc5').set('hgap', 'Cu_Cu2');
model.component('comp1').physics('ht').feature('tc5').active(false);
model.component('comp1').physics('ht').feature('tc5').label([native2unicode(hex2dec({'63' 'a5'}), 'unicode')  native2unicode(hex2dec({'89' 'e6'}), 'unicode')  native2unicode(hex2dec({'70' 'ed'}), 'unicode')  native2unicode(hex2dec({'96' '3b'}), 'unicode') '(Cu_Cu) 2']);
model.component('comp1').physics('ht').feature('pc1').active(false);
model.component('comp1').physics('ht').feature('pc1').label([native2unicode(hex2dec({'5d' 'e6'}), 'unicode')  native2unicode(hex2dec({'53' 'f3'}), 'unicode')  native2unicode(hex2dec({'4e' '24'}), 'unicode')  native2unicode(hex2dec({'4f' 'a7'}), 'unicode')  native2unicode(hex2dec({'7b' '49'}), 'unicode')  native2unicode(hex2dec({'6e' '29'}), 'unicode') ]);

model.study.create('std1');
model.study('std1').create('time', 'Transient');

model.sol.create('sol1');
model.sol('sol1').attach('std1');
model.sol('sol1').create('st1', 'StudyStep');
model.sol('sol1').create('v1', 'Variables');
model.sol('sol1').create('t1', 'Time');
model.sol('sol1').feature('t1').create('fc1', 'FullyCoupled');
model.sol('sol1').feature('t1').create('d1', 'Direct');
model.sol('sol1').feature('t1').create('i1', 'Iterative');
model.sol('sol1').feature('t1').feature('i1').create('mg1', 'Multigrid');
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('pr').create('so1', 'SOR');
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('po').create('so1', 'SOR');
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('cs').create('d1', 'Direct');
model.sol('sol1').feature('t1').feature.remove('fcDef');

model.result.dataset.create('cln1', 'CutLine2D');
model.result.dataset.create('cpt1', 'CutPoint2D');
model.result.create('pg1', 'PlotGroup2D');
model.result.create('pg2', 'PlotGroup1D');
model.result.create('pg6', 'PlotGroup1D');
model.result('pg1').create('surf1', 'Surface');
model.result('pg1').create('con1', 'Contour');
model.result('pg2').create('ptgr1', 'PointGraph');
model.result('pg6').create('lngr1', 'LineGraph');
model.result.export.create('data1', 'Data');

model.study('std1').feature('time').set('tlist', '10^{range(log10(1.0e-8),1/100,log10(0.0001))}');

model.sol('sol1').feature('st1').label([native2unicode(hex2dec({'7f' '16'}), 'unicode')  native2unicode(hex2dec({'8b' 'd1'}), 'unicode')  native2unicode(hex2dec({'65' 'b9'}), 'unicode')  native2unicode(hex2dec({'7a' '0b'}), 'unicode') ': ' native2unicode(hex2dec({'77' 'ac'}), 'unicode')  native2unicode(hex2dec({'60' '01'}), 'unicode') ]);
model.sol('sol1').feature('v1').label([native2unicode(hex2dec({'56' 'e0'}), 'unicode')  native2unicode(hex2dec({'53' 'd8'}), 'unicode')  native2unicode(hex2dec({'91' 'cf'}), 'unicode') ' 1.1']);
model.sol('sol1').feature('v1').set('resscalemethod', 'manual');
model.sol('sol1').feature('v1').set('clist', {'{10^{range(log10(1.0e-8), 1/100, log10(0.0001))}}[s]' '9.999000000000001E-8[s]'});
model.sol('sol1').feature('t1').label([native2unicode(hex2dec({'77' 'ac'}), 'unicode')  native2unicode(hex2dec({'60' '01'}), 'unicode')  native2unicode(hex2dec({'6c' '42'}), 'unicode')  native2unicode(hex2dec({'89' 'e3'}), 'unicode')  native2unicode(hex2dec({'56' '68'}), 'unicode') ' 1.1']);
model.sol('sol1').feature('t1').set('tlist', '10^{range(log10(1.0e-8),1/100,log10(0.0001))}');
model.sol('sol1').feature('t1').set('tstepsbdf', 'strict');
model.sol('sol1').feature('t1').set('maxorder', 2);
model.sol('sol1').feature('t1').set('estrat', 'exclude');
model.sol('sol1').feature('t1').feature('dDef').label([native2unicode(hex2dec({'76' 'f4'}), 'unicode')  native2unicode(hex2dec({'63' 'a5'}), 'unicode') ' 2']);
model.sol('sol1').feature('t1').feature('aDef').label([native2unicode(hex2dec({'9a' 'd8'}), 'unicode')  native2unicode(hex2dec({'7e' 'a7'}), 'unicode') ' 1']);
model.sol('sol1').feature('t1').feature('fc1').label([native2unicode(hex2dec({'51' '68'}), 'unicode')  native2unicode(hex2dec({'80' '26'}), 'unicode')  native2unicode(hex2dec({'54' '08'}), 'unicode') ' 1.1']);
model.sol('sol1').feature('t1').feature('fc1').set('linsolver', 'd1');
model.sol('sol1').feature('t1').feature('fc1').set('jtech', 'once');
model.sol('sol1').feature('t1').feature('fc1').set('stabacc', 'aacc');
model.sol('sol1').feature('t1').feature('fc1').set('aaccdim', 5);
model.sol('sol1').feature('t1').feature('fc1').set('aaccmix', 0.9);
model.sol('sol1').feature('t1').feature('fc1').set('aaccdelay', 1);
model.sol('sol1').feature('t1').feature('fc1').set('damp', '0.9');
model.sol('sol1').feature('t1').feature('d1').label([native2unicode(hex2dec({'76' 'f4'}), 'unicode')  native2unicode(hex2dec({'63' 'a5'}), 'unicode')  native2unicode(hex2dec({'ff' '0c'}), 'unicode')  native2unicode(hex2dec({'4f' '20'}), 'unicode')  native2unicode(hex2dec({'70' 'ed'}), 'unicode')  native2unicode(hex2dec({'53' 'd8'}), 'unicode')  native2unicode(hex2dec({'91' 'cf'}), 'unicode') ' (ht)']);
model.sol('sol1').feature('t1').feature('d1').set('linsolver', 'pardiso');
model.sol('sol1').feature('t1').feature('d1').set('pivotperturb', 1.0E-13);
model.sol('sol1').feature('t1').feature('i1').label(['AMG' native2unicode(hex2dec({'ff' '0c'}), 'unicode')  native2unicode(hex2dec({'4f' '20'}), 'unicode')  native2unicode(hex2dec({'70' 'ed'}), 'unicode')  native2unicode(hex2dec({'53' 'd8'}), 'unicode')  native2unicode(hex2dec({'91' 'cf'}), 'unicode') ' (ht)']);
model.sol('sol1').feature('t1').feature('i1').set('rhob', 20);
model.sol('sol1').feature('t1').feature('i1').feature('ilDef').label([native2unicode(hex2dec({'4e' '0d'}), 'unicode')  native2unicode(hex2dec({'5b' '8c'}), 'unicode')  native2unicode(hex2dec({'51' '68'}), 'unicode') ' LU ' native2unicode(hex2dec({'52' '06'}), 'unicode')  native2unicode(hex2dec({'89' 'e3'}), 'unicode') ' 1']);
model.sol('sol1').feature('t1').feature('i1').feature('mg1').label([native2unicode(hex2dec({'59' '1a'}), 'unicode')  native2unicode(hex2dec({'91' 'cd'}), 'unicode')  native2unicode(hex2dec({'7f' '51'}), 'unicode')  native2unicode(hex2dec({'68' '3c'}), 'unicode') ' 1.1']);
model.sol('sol1').feature('t1').feature('i1').feature('mg1').set('prefun', 'saamg');
model.sol('sol1').feature('t1').feature('i1').feature('mg1').set('maxcoarsedof', 50000);
model.sol('sol1').feature('t1').feature('i1').feature('mg1').set('saamgcompwise', true);
model.sol('sol1').feature('t1').feature('i1').feature('mg1').set('usesmooth', false);
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('pr').label([native2unicode(hex2dec({'98' '84'}), 'unicode')  native2unicode(hex2dec({'5e' '73'}), 'unicode')  native2unicode(hex2dec({'6e' 'd1'}), 'unicode')  native2unicode(hex2dec({'56' '68'}), 'unicode') ' 1']);
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('pr').feature('soDef').label('SOR 2');
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('pr').feature('so1').label('SOR 1.1');
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('pr').feature('so1').set('relax', 0.9);
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('po').label([native2unicode(hex2dec({'54' '0e'}), 'unicode')  native2unicode(hex2dec({'5e' '73'}), 'unicode')  native2unicode(hex2dec({'6e' 'd1'}), 'unicode')  native2unicode(hex2dec({'56' '68'}), 'unicode') ' 1']);
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('po').feature('soDef').label('SOR 2');
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('po').feature('so1').label('SOR 1.1');
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('po').feature('so1').set('relax', 0.9);
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('cs').label([native2unicode(hex2dec({'7c' '97'}), 'unicode')  native2unicode(hex2dec({'53' '16'}), 'unicode')  native2unicode(hex2dec({'6c' '42'}), 'unicode')  native2unicode(hex2dec({'89' 'e3'}), 'unicode')  native2unicode(hex2dec({'56' '68'}), 'unicode') ' 1']);
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('cs').feature('dDef').label([native2unicode(hex2dec({'76' 'f4'}), 'unicode')  native2unicode(hex2dec({'63' 'a5'}), 'unicode') ' 2']);
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('cs').feature('d1').label([native2unicode(hex2dec({'76' 'f4'}), 'unicode')  native2unicode(hex2dec({'63' 'a5'}), 'unicode') ' 1.1']);
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('cs').feature('d1').set('linsolver', 'pardiso');
model.sol('sol1').feature('t1').feature('i1').feature('mg1').feature('cs').feature('d1').set('pivotperturb', 1.0E-13);

model.study('std1').runNoGen;

model.result.dataset('cln1').set('genpoints', {'-2.5' '0.05-(1-(h_Cu_B+0.2))+0.05'; '2.5' '0.05-(1-(h_Cu_B+0.2))+0.05'});
model.result.dataset('cpt1').label([native2unicode(hex2dec({'4e' '2d'}), 'unicode')  native2unicode(hex2dec({'5f' 'c3'}), 'unicode')  native2unicode(hex2dec({'70' 'b9'}), 'unicode') ]);
model.result.dataset('cpt1').set('pointx', '(cupper_A_pos+cupper_C_pos)/2');
model.result.dataset('cpt1').set('pointy', '0.05-(1-(h_Cu_B+0.2))+0.05');
model.result('pg1').label([native2unicode(hex2dec({'6e' '29'}), 'unicode')  native2unicode(hex2dec({'5e' 'a6'}), 'unicode') ' (ht)']);
model.result('pg1').set('looplevel', {'last'});
model.result('pg1').feature('surf1').set('resolution', 'normal');
model.result('pg1').feature('con1').active(false);
model.result('pg1').feature('con1').set('coloring', 'uniform');
model.result('pg1').feature('con1').set('color', 'white');
model.result('pg1').feature('con1').set('resolution', 'normal');
model.result('pg2').label([native2unicode(hex2dec({'4e' '2d'}), 'unicode')  native2unicode(hex2dec({'5f' 'c3'}), 'unicode')  native2unicode(hex2dec({'70' 'b9'}), 'unicode')  native2unicode(hex2dec({'76' '84'}), 'unicode')  native2unicode(hex2dec({'6e' '29'}), 'unicode')  native2unicode(hex2dec({'5e' 'a6'}), 'unicode')  native2unicode(hex2dec({'6f' '14'}), 'unicode')  native2unicode(hex2dec({'53' '16'}), 'unicode') ]);
model.result('pg2').set('looplevelinput', {'manual'});
model.result('pg2').set('looplevel', [1]);
model.result('pg2').set('xlabel', [native2unicode(hex2dec({'65' 'f6'}), 'unicode')  native2unicode(hex2dec({'95' 'f4'}), 'unicode') ' (s)']);
model.result('pg2').set('ylabel', [native2unicode(hex2dec({'6e' '29'}), 'unicode')  native2unicode(hex2dec({'5e' 'a6'}), 'unicode') ' (K)']);
model.result('pg2').set('xlog', true);
model.result('pg2').set('axisactive', true);
model.result('pg2').set('axisprecision', 8);
model.result('pg2').set('xlabelactive', false);
model.result('pg2').set('ylabelactive', false);
model.result('pg2').feature('ptgr1').set('data', 'cpt1');
model.result('pg2').feature('ptgr1').set('looplevelinput', {'all'});
model.result('pg2').feature('ptgr1').set('linewidth', 'preference');
model.result('pg6').set('xlabel', [native2unicode(hex2dec({'5f' '27'}), 'unicode')  native2unicode(hex2dec({'95' '7f'}), 'unicode') ' (' native2unicode(hex2dec({'00' 'b5'}), 'unicode') 'm)']);
model.result('pg6').set('ylabel', [native2unicode(hex2dec({'6e' '29'}), 'unicode')  native2unicode(hex2dec({'5e' 'a6'}), 'unicode') ' (K)']);
model.result('pg6').set('xlabelactive', false);
model.result('pg6').set('ylabelactive', false);
model.result('pg6').feature('lngr1').set('data', 'cln1');
model.result('pg6').feature('lngr1').set('looplevelinput', {'all'});
model.result('pg6').feature('lngr1').set('linewidth', 'preference');
model.result('pg6').feature('lngr1').set('resolution', 'normal');
        % 运行求解
        fprintf('开始求解第 %d 组参数...\n', i);
        model.study('std1').runNoGen;
        fprintf('求解完成。\n');

        % 生成文件名（参考run_comsol_and_process_final_v2.m的命名规则）
        filename = generate_filename_from_params(current_params);
        output_file = fullfile(output_dir, [filename '.csv']);

        % 设置导出参数
        model.result.export('data1').set('data', 'cln1');
        model.result.export('data1').set('filename', output_file);
        model.result.export('data1').set('resolution', 'finer');
        model.result.export('data1').set('separator', ',');

        % 导出结果
        fprintf('导出结果到文件: %s\n', output_file);
        model.result.export('data1').run();

        fprintf('第 %d 组参数处理完成。\n', i);

    catch ME
        fprintf('第 %d 组参数处理失败: %s\n', i, ME.message);
        if ~isempty(ME.stack)
            fprintf('错误位置: %s (第 %d 行)\n', ME.stack(1).name, ME.stack(1).line);
        end
        continue;
    end
end

fprintf('\n批量仿真完成！共处理 %d 组参数。\n', total_runs);
out = [];

end

% 生成文件名的辅助函数（参考run_comsol_and_process_final_v2.m）
function filename = generate_filename_from_params(params)
    % 将小数点替换为'p'，负号替换为'n'
    cu1_str = strrep(strrep(num2str(params.Cu_Cu1,'%g'),'.','p'),'+','');
    cupper_A_str = strrep(strrep(num2str(params.cupper_A_pos,'%.4f'),'.','p'),'-','n');
    cupper_C_str = strrep(strrep(num2str(params.cupper_C_pos,'%.4f'),'.','p'),'-','n');
    L_Cu_A_str = strrep(num2str(params.L_Cu_A,'%.2f'),'.','p');
    L_Cu_C_str = strrep(num2str(params.L_Cu_C,'%.2f'),'.','p');
    h_Cu_AA_str = strrep(num2str(params.h_Cu_AA,'%.2f'),'.','p');
    h_Cu_CC_str = strrep(num2str(params.h_Cu_CC,'%.2f'),'.','p');

    filename = sprintf('Cu1_%s_APos_%s_CPos_%s_LA_%s_LC_%s_hAA_%s_hCC_%s', ...
        cu1_str, cupper_A_str, cupper_C_str, L_Cu_A_str, L_Cu_C_str, h_Cu_AA_str, h_Cu_CC_str);
end
