# COMSOL批量仿真使用说明

## 概述
本项目实现了基于`generated_parameters.csv`参数文件的COMSOL批量仿真功能。系统会自动读取CSV文件中的参数，进行批量计算，并根据参数值生成对应的CSV结果文件。

## 文件说明

### 主要文件
- `M2025_8_30.m` - 主要的批量仿真脚本
- `generated_parameters.csv` - 由`generate_parameters.m`生成的参数文件（10,000组参数）
- `generate_parameters.m` - 参数生成脚本
- `run_comsol_and_process_final_v2.m` - 参考的CSV命名规则和导出方法

### 测试文件
- `test_parameter_processing.m` - 参数处理测试脚本
- `test_parameters.csv` - 测试用的小参数文件
- `test_results/` - 测试结果目录

## 参数映射

### CSV文件中的参数对应关系
从`generated_parameters.csv`读取的参数会映射到COMSOL模型中的对应参数：

| CSV列名 | COMSOL参数名 | 说明 |
|---------|-------------|------|
| Cu_Cu1 | Cu_Cu1 | 铜-铜接触热阻1 |
| cupper_A_pos | cupper_A_pos | 铜块A位置 |
| cupper_C_pos | cupper_C_pos | 铜块C位置 |
| L_Cu_A | L_Cu_A | 铜块A长度 |
| L_Cu_C | L_Cu_C | 铜块C长度 |
| h_Cu_AA | h_Cu_A, h_Cu_AA | 铜块A高度 |
| h_Cu_CC | h_Cu_C, h_Cu_CC | 铜块C高度 |

### 固定参数
以下参数在批量仿真中保持固定值：
- AMP = 1e9
- cupper_width = 5
- cupper_height = 2
- cupper_B_pos = 1.5
- cupper_D_pos = 1.4
- Cu_SiO2 = 9E+07
- SiO2_SiO2 = 4.00E+08
- Si_SiO2 = 2.8e7, 6e8
- Cu_Si = 2.58e8
- Si_Si = 2e8
- Cu_Cu2 = 60000000
- h_Cu_B = 0.41
- h_Cu_D = 0.51
- L_Cu_B = 3
- L_Cu_D = 3

## CSV文件命名规则

结果文件按以下规则命名（参考`run_comsol_and_process_final_v2.m`）：
```
Cu1_{Cu_Cu1值}_APos_{cupper_A_pos值}_CPos_{cupper_C_pos值}_LA_{L_Cu_A值}_LC_{L_Cu_C值}_hAA_{h_Cu_AA值}_hCC_{h_Cu_CC值}.csv
```

### 命名规则详细说明
- 小数点 `.` 替换为 `p`
- 负号 `-` 替换为 `n`
- 正号 `+` 被移除
- 科学计数法保持原样（如1e06）

### 示例
参数值：
- Cu_Cu1 = 7245465.89460901
- cupper_A_pos = -0.301584383100095
- cupper_C_pos = -0.240883851334717
- L_Cu_A = 2.23274983073531
- L_Cu_C = 2.91166803013827
- h_Cu_AA = 0.472812326031699
- h_Cu_CC = 0.426576555997902

生成的文件名：
```
Cu1_7p24547e06_APos_n0p3016_CPos_n0p2409_LA_2p23_LC_2p91_hAA_0p47_hCC_0p43.csv
```

## 使用方法

### 1. 准备工作
确保以下文件存在：
- `generated_parameters.csv` - 参数文件
- `M2025_8_30.m` - 批量仿真脚本

### 2. 运行批量仿真
在MATLAB中执行：
```matlab
% 启动COMSOL服务器（如果需要）
% 然后运行批量仿真
result = M2025_8_30();
```

### 3. 输出结果
- 结果文件保存在 `batch_results/` 目录中
- 每个参数组合对应一个CSV文件
- 文件名根据参数值自动生成

## 测试功能

### 参数处理测试
运行测试脚本验证参数读取和文件名生成：
```matlab
test_parameter_processing();
```

这将：
1. 读取`test_parameters.csv`中的测试参数
2. 验证参数解析功能
3. 生成示例文件名
4. 在`test_results/`目录中创建示例文件

## 注意事项

1. **COMSOL环境**：运行前需要确保COMSOL服务器正常启动
2. **内存管理**：批量处理大量参数时注意内存使用
3. **错误处理**：脚本包含错误处理机制，单个参数失败不会影响整体进程
4. **文件路径**：确保有足够的磁盘空间存储结果文件
5. **参数验证**：建议先用少量参数测试，确认无误后再进行大规模批量处理

## 故障排除

### 常见问题
1. **"无法解析名称 'ModelUtil.clear'"**：需要先启动COMSOL服务器
2. **"未找到参数文件"**：确保`generated_parameters.csv`文件存在
3. **内存不足**：可以分批处理参数，或增加MATLAB内存限制

### 调试建议
1. 先运行`test_parameter_processing()`验证参数读取功能
2. 使用小的测试参数文件验证COMSOL连接
3. 检查生成的文件名是否符合预期格式
